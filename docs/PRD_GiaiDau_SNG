# PRD Giải đấu SNG (Sit And Go)

## 1. <PERSON><PERSON><PERSON> bản

### 1.1. <PERSON><PERSON><PERSON> ký giải đấu SNG

- <PERSON><PERSON> đăng ký: <PERSON><PERSON> đăng ký bao gồm <PERSON>í báo danh + phí dịch vụ
  - <PERSON><PERSON> báo danh: cộng vào quỹ thưởng
  - <PERSON><PERSON> dịch vụ: không cộng vào quỹ thưởng
- <PERSON>àn phí đăng ký:
  - Đ<PERSON> tham gia: không hoàn phí đăng ký
  - Đăng ký nhưng không tham gia: hoàn 80% phí đăng ký

### 1.2. <PERSON><PERSON><PERSON> nghĩa các loại Sân và phí tương ứng

- <PERSON>ân 5 người
  - <PERSON><PERSON> sơ cấp:
    - <PERSON><PERSON> báo danh: 50M (50000000)
    - phí dịch vụ: 5M (5000000)
  - Sân trung cấp:
    - <PERSON><PERSON> báo danh: 100M (100000000)
    - p<PERSON><PERSON> dịch vụ: 10M (10000000)
  - <PERSON><PERSON> cấp:
    - <PERSON><PERSON> báo danh: 200M (200000000)
    - <PERSON><PERSON><PERSON> dịch vụ: 20M (20000000)
  - Sân Pro:
    - <PERSON><PERSON> báo danh: 500M (500000000)
    - phí dịch vụ: 50M (50000000)
- Sân 9 người
  - Sân sơ cấp:
    - Phí báo danh: 50M (50000000)
    - phí dịch vụ: 5M (5000000)
  - Sân trung cấp:
    - Phí báo danh: 100M (100000000)
    - phí dịch vụ: 10M (10000000)
  - Sân Cao cấp:
    - Phí báo danh: 200M (200000000)
    - phí dịch vụ: 20M (20000000)
  - Sân Pro:
    - Phí báo danh: 500M (500000000)
    - phí dịch vụ: 50M (50000000)

### 1.3. Quy tắc chơi

- Số Người Chơi: Giải đấu sẽ bắt đầu nếu đủ 5 người chơi (nếu chọn sân 5) hoặc 9 người chơi (nếu chọn sân 9).
- Chip Bắt Đầu: Mỗi người chơi sẽ nhận 100M chip để bắt đầu
- Blind:
  - Blind khởi đầu là 500k/1M
  - Blind sẽ tăng lên gấp 2 lần sau mỗi 5 phút.
  - Khi blind đạt mức cao, hệ thống sẽ áp dụng ante để tăng thêm mức cược. Chú ý Ante này có thể cấu hình và on/off tùy rule của từng nhà cái
  - Table Blind chi tiết dưới đây
    - Level 1:
      - Blind: 500k / 1M
      - Ante: 0
      - Thời gian: 0 - 5 phút
    - Level 2:
      - Blind: 1M / 2M
      - Ante: 0
      - Thời gian: 5 - 10 phút
    - Level 3:
      - Blind: 2M / 4M
      - Ante: 0
      - Thời gian: 10 - 15 phút
    - Level 4:
      - Blind: 4M / 8M
      - Ante: 0
      - Thời gian: 15 - 20 phút
    - Level 5:
      - Blind: 8M / 16M
      - Ante: 100k
      - Thời gian: 20 - 25 phút
    - Level 6:
      - Blind: 16M / 32M
      - Ante: 200k
      - Thời gian: 25 - 30 phút
    - Level 7:
      - Blind: 32M / 64M
      - Ante: 300k
      - Thời gian: 30 - 35 phút
    - Level 8:
      - Blind: 64M / 128M
      - Ante: 400k
      - Thời gian: 35 - 40 phút
    - Level 9:
      - Blind: 128M / 256M
      - Ante: 500k
      - Thời gian: 40 - 45 phút
- tổng quan:

  - Blind cứ tiếp tục tăng 2 lần sau mỗi 5 phút
  - Ante: cứ tiếp tục tăng 100k sau mỗi 5 phút

- Loại trừ:
  - Khi một người chơi không còn chip (0 chip), họ sẽ bị loại khỏi giải đấu.
  - Trong trường hợp nhiều người chơi loại cùng lúc (tức là cùng một ván bài), có nhiều cách tính thứ hạng khác nhau tuỳ theo rule của nhà cái:
    - Cách 1: xem xét thứ hạng dựa trên số chip còn lại trước khi vòng cược kết thúc.
    - Cách 2: người chơi bị loại cùng lúc sẽ đồng hạng.
    - Cách 3: xem xét thứ hạng dựa trên thứ tự tham gia giải đấu.
- Mua lại: Không có tùy chọn mua lại trong suốt giải đấu.

### 1.4. Luật chơi

- Người chơi sẽ được chia hai lá bài riêng và sẽ tham gia vào các vòng cược: Pre-flop, Flop, Turn và River.
- Các vòng cược sẽ diễn ra theo chiều kim đồng hồ. Người chơi có thể chọn cược, theo, tăng hoặc bỏ bài.
- Bài chung sẽ được lật theo thứ tự: Flop (ba lá), Turn (một lá) và River (một lá).
- Người chơi có thể dùng bài riêng và bài chung để tạo thành tay bài tốt nhất.

### 1.5. Quy tắc chia thưởng (giải thưởng)

- 1st Place: 50% quỹ giải thưởng
- 2nd Place: 30% quỹ giải thưởng
- 3rd Place: 20% quỹ giải thưởng

Kết Thúc Giải Đấu:

- Giải đấu sẽ kết thúc khi chỉ còn một người chơi duy nhất còn chip.
- Giải thưởng sẽ được chia theo tỷ lệ quy định.

### 1.6. Logic luồng hoạt động

1. Người chơi đăng ký vào bàn SNG -> trừ Buy-in và phí dịch vụ (tự động).
2. Khi đủ số lượng người (6 hoặc 9), chuyển trạng thái -> READY.
3. Hệ thống countdown 5 giây để chuẩn bị.
4. Khởi động bàn: xáo bài, chia ghế ngẫu nhiên, chỉ định Dealer.
5. Vào vòng chơi bình thường, quản lý vòng cược, pot và loại người chơi khi hết chip.
6. Mỗi 5 phút, hệ thống tăng blind tự động.
7. Khi chỉ còn 1 người giữ chip -> kết thúc bàn, phân phối giải thưởng.

## 2. Công nghệ sử dụng trong dự án

- Nodejs javascript, sử dụng Pomelo framework
- Mysql 8.3
- Redis
- Rabbitmq

## 3. Flow triển khai

- đọc logic hiện tại của dự án
- đọc logic về toàn bộ bàn chơi (của bàn thường) từ khởi tạo danh sách bàn chơi, các luồng logic trong 1 bàn chơi, logic 1 ván chơi qua các vòng: khởi tạo bàn, tạo bài, thêm/xóa người chơi, các hành động của người chơi, tính toán kết quả ván chơi, tính toán tiền thưởng, ...trong `game-server/app/servers/game/handler/tableHandler.js`, `game-server/app/services/tableService.js`, logic game các file trong folder này `game-server/app/game/*`, và các logic liên quan để làm việc với database trong `game-server/app/services/dbService.js`, `game-server/app/controllers/dbManager.js`
- với các talbes mới phát sinh thì tạo migrations, models, services, DTO như các tables đã có (tham khảo cấu trúc hiện tại của dự án để follow theo)
- các event trả về cho client được định nghĩa trong folder `game-server/app/consts/consts.js`
- Dựa vào đó triển khai luồng logic cho SNG tournaments với các mong muốn và yêu cầu sau:

  - Tách biệt với luồng bàn chơi hiện tại (để tránh , giảm thiểu rủi ro và xung đột với bàn chơi tự do đang có)
  - Cố gắng sử dụng lại càng nhiều logic của bàn chơi hiện tại càng tốt (để giảm thiểu việc viết code mới và cho Client game không phải thay đổi nhiều logic)
  - Data Response trả về ở các onEvent trong bàn chơi cố gắng giữ giống format hiện tại đang có để cho client không phải thay đổi nhiều logic, cụ thể tham khảo ở đây (chỉ xem cấu trúc và logic lưu dữ liệu), bạn hoàn toàn có thể thêm cho phù hợp với luồng logic mới:

  ```json
  {
    "state": "IN_PROGRESS",
    "id": "1e663270-30c5-11f0-9400-5dedf132f96b",
    "tid": "97c64180-2f5a-11f0-9400-5dedf132f96b",
    "creator": 1,
    "dealer": 0,
    "smallBlind": 2000,
    "bigBlind": 4000,
    "minPlayers": 2,
    "maxPlayers": 5,
    "minBuyIn": 40000,
    "maxBuyIn": 800000,
    "gameMode": "normal",
    "isShowBoard": true,
    "players": [
      {
        "playerName": "dvhoanganh2",
        "id": 25,
        "chips": 404000,
        "folded": false,
        "allIn": false,
        "talked": false,
        "type": "WEB",
        "avatar": "10",
        "level": 4,
        "vippoint": 0,
        "exp": 316,
        "actorNr": 1,
        "index": 0,
        "isState": true,
        "hand": {
          "cards": ["3H", "3D", "9C", "AD", "2S"],
          "rank": 10.2112,
          "message": "Pair",
          "code": 601
        },
        "cards": ["3H", "3D"]
      },
      {
        "playerName": "dvlokqwm",
        "id": 485,
        "chips": 396000,
        "folded": true,
        "allIn": false,
        "talked": true,
        "type": "WEB",
        "avatar": "1",
        "level": 0,
        "vippoint": 0,
        "exp": 0,
        "actorNr": 3,
        "index": 1,
        "isState": true,
        "hand": {
          "cards": ["JS", "7S", "9C", "AD", "2S"],
          "rank": 8.0336,
          "message": "High Card",
          "code": 600
        },
        "cards": ["JS", "7S"]
      }
    ],
    "playersToRemove": [],
    "playersToAdd": [],
    "gameWinners": [
      {
        "playerName": "dvhoanganh2",
        "id": 25,
        "chips": 404000,
        "amount": 8000,
        "index": 0,
        "hand": { "rank": 10.2112, "message": "Pair" },
        "players": [
          {
            "id": 25,
            "playerName": "dvhoanganh2",
            "actorNr": 1,
            "cards": ["3H", "3D"],
            "hand": {
              "cards": ["3H", "3D", "9C", "AD", "2S"],
              "rank": 10.2112,
              "message": "Pair",
              "code": 601
            },
            "folded": false,
            "allIn": false,
            "chips": 404000,
            "oldChips": 400000,
            "handInfo": ["3H", "3D", "AD", "9C", "2S"],
            "amount": 4000,
            "isWinner": true
          },
          {
            "id": 485,
            "playerName": "dvlokqwm",
            "actorNr": 3,
            "cards": ["JS", "7S"],
            "hand": {
              "cards": ["JS", "7S", "9C", "AD", "2S"],
              "rank": 8.0336,
              "message": "High Card",
              "code": 600
            },
            "folded": true,
            "allIn": false,
            "chips": 396000,
            "oldChips": 400000,
            "handInfo": ["AD", "JS", "9C", "7S", "2S"],
            "amount": -4000,
            "isWinner": false
          }
        ],
        "broad": ["9C", "AD", "2S"]
      }
    ],
    "playersToNotEnough": [],
    "actions": [
      { "action": "call", "playerName": "dvhoanganh2", "amount": 4000 },
      { "action": "call", "playerName": "dvlokqwm", "amount": 4000 },
      { "action": "fold", "playerName": "dvlokqwm" }
    ],
    "game": {
      "smallBlind": 2000,
      "bigBlind": 4000,
      "pot": 0,
      "sidePot": 0,
      "sidepots": [
        {
          "amount": 8000,
          "eligiblePlayers": [25],
          "isMainPot": true,
          "winners": [
            {
              "id": 25,
              "playerName": "dvhoanganh2",
              "amount": 8000,
              "hand": { "rank": 10.2112, "message": "Pair" }
            }
          ]
        }
      ],
      "roundName": "GameEnd",
      "betName": "bet",
      "bets": [],
      "roundBets": [0, 0],
      "board": ["9C", "AD", "2S"],
      "incrRaise": 4000,
      "blinds": [0, 1]
    },
    "board": ["9C", "AD", "2S"],
    "currentPlayer": 1,
    "currentPlayerTime": 1747228383
  }
  ```

## 4. THIẾT KẾ DATABASE (MySQL)

### 4.1. sng_tournaments – Thông tin từng giải đấu SNG

```sql
CREATE TABLE `sng_tournaments` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `code` VARCHAR(100) UNIQUE,          -- Mã giải đấu: sng_20250515_001
  `status` ENUM('WAITING', 'READY', 'IN_PROGRESS', 'ENDED') NOT NULL,
  `player_capacity` TINYINT NOT NULL DEFAULT 6,  -- 6 or 9
  `buy_in` INT UNSIGNED NOT NULL,               -- Số chips tham gia
  `fee` INT UNSIGNED NOT NULL DEFAULT 0,        -- Phí dịch vụ
  `reward_pool` INT UNSIGNED DEFAULT 0,         -- Tổng pool thưởng
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  `started_at` DATETIME,
  `ended_at` DATETIME
);
```

### 4.2. sng_tournament_players – Liên kết người chơi với giải đấu

```sql
CREATE TABLE `sng_tournament_players` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tournament_id` BIGINT UNSIGNED NOT NULL,
  `player_id` BIGINT UNSIGNED NOT NULL,         -- FK đến bảng `players`
  `seat_number` TINYINT,                        -- Vị trí ngồi tại bàn
  `initial_chips` INT UNSIGNED NOT NULL,
  `current_chips` INT UNSIGNED NOT NULL,
  `status` ENUM('ACTIVE', 'ELIMINATED', 'WINNER') DEFAULT 'ACTIVE',
  `eliminated_at_hand` INT,
  `rank` TINYINT,                               -- Thứ hạng sau khi kết thúc
  `joined_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`tournament_id`) REFERENCES `sng_tournaments`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`player_id`) REFERENCES `players`(`id`) ON DELETE CASCADE
);
```

### 4.3. sng_blind_levels – Lưu cấu trúc tăng blind theo level

```sql
CREATE TABLE `sng_blind_levels` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tournament_id` BIGINT UNSIGNED NOT NULL,
  `level_number` TINYINT NOT NULL,
  `small_blind` INT UNSIGNED NOT NULL,
  `big_blind` INT UNSIGNED NOT NULL,
  `ante` INT UNSIGNED DEFAULT 0,
  `duration_seconds` INT UNSIGNED DEFAULT 300, -- 5 phút
  FOREIGN KEY (`tournament_id`) REFERENCES `sng_tournaments`(`id`) ON DELETE CASCADE
);
```

### 4.4. sng_hand_histories – Lưu tay bài đã diễn ra (nếu cần replay/debug)

```sql
CREATE TABLE `sng_hand_histories` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tournament_id` BIGINT UNSIGNED NOT NULL,
  `hand_number` INT NOT NULL,
  `dealer_seat` TINYINT,
  `actions_json` JSON,                    -- Ghi lại chi tiết hành động theo từng vòng
  `pot` INT UNSIGNED,
  `winner_seat` TINYINT,
  `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`tournament_id`) REFERENCES `sng_tournaments`(`id`) ON DELETE CASCADE
);
```

### 4.5. sng_rewards – Ghi lại phần thưởng nhận được khi kết thúc

```sql
CREATE TABLE `sng_rewards` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tournament_id` BIGINT UNSIGNED NOT NULL,
  `player_id` BIGINT UNSIGNED NOT NULL,
  `rank` TINYINT NOT NULL,
  `reward_amount` INT UNSIGNED NOT NULL,
  `rewarded_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`tournament_id`) REFERENCES `sng_tournaments`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`player_id`) REFERENCES `players`(`id`) ON DELETE CASCADE
);
```

### 4.6. MỐI LIÊN KẾT GIỮA CÁC BẢNG

```bash
players ─────────────┐
                     │
sng_tournament_players ───> sng_tournaments ───> sng_blind_levels
                                           └───> sng_hand_histories
                                           └───> sng_rewards
```

## 5. Định nghĩa các hàm cần xử lý

- Cố gắng bám sát để tạo các function handler gống như của `app/servers/game/handler/tableHandler.js`
- Danh sách các bàn cũng cần tạo thành dạng template file config giống như danh sách bàn của cách chơi tự do đang có
- Hãy đề xuất hoặc đưa phương án viết script hoặc cách nào để có thể tự test được luồng dữ liệu/logic của các hàm design, toàn bộ logic 1 ván chơi của SNG tournaments
- chú ý với hàm lấy danh sách các bàn nhớ trả thêm tổng số người đã đăng ký giải đấu nhé

## 6. Chú ý khác

- Node sử dụng là node v18.18.0
- cách chạy server là từ thư mục gốc `game-server` gõ :

```bash
./node_modules/.bin/pomelo start
```

# SNG Tournament Feature

## Overview

The SNG (Sit and Go) Tournament feature allows players to participate in poker tournaments with a fixed number of players. Unlike regular cash games, SNG tournaments have a fixed buy-in amount, and players compete until one player has all the chips. The tournament ends when only one player remains, and prizes are awarded based on final rankings.

## Key Features

- Fixed buy-in amount with tournament fee
- Fixed number of players (5 or 9)
- Increasing blind levels at regular intervals
- Elimination of players when they run out of chips
- Prize distribution based on final rankings
- Tournament history and statistics

## Database Structure

The SNG tournament feature uses the following database tables:

1. `sng_tournaments` - Stores tournament information
2. `sng_tournament_players` - Stores player participation and status
3. `sng_blind_levels` - Stores blind level information for each tournament
4. `sng_hand_histories` - Stores hand history information for each tournament
5. `sng_rewards` - Stores reward information for tournament winners
6. `sng_tournament_logs` - Stores detailed logs of all tournament actions and events

## Implementation Details

### Tournament States

- `WAITING` - Tournament is waiting for players to register
- `READY` - Tournament has enough players and is ready to start
- `IN_PROGRESS` - Tournament is in progress
- `ENDED` - Tournament has ended

### Player States

- `ACTIVE` - Player is still active in the tournament
- `ELIMINATED` - Player has been eliminated from the tournament
- `WINNER` - Player is the winner of the tournament

### Blind Levels

Blind levels increase at regular intervals (default: 5 minutes). Each level has:

- Small blind amount
- Big blind amount
- Ante amount (optional)
- Duration in seconds

### Prize Distribution

The prize pool is distributed among the top players based on their final rankings:

- 1st place: 50% of prize pool
- 2nd place: 30% of prize pool
- 3rd place: 20% of prize pool

## API Endpoints

### Tournament Management

- `game.sngTournamentHandler.getTournaments` - Get list of available tournaments
- `game.sngTournamentHandler.getTournament` - Get tournament details
- `game.sngTournamentHandler.createTournament` - Create a new tournament (admin only)
- `game.sngTournamentHandler.registerTournament` - Register for a tournament by type and level
- `game.sngTournamentHandler.leaveTournament` - Leave a tournament (automatically detects which tournament the player is registered for)
- `game.sngTournamentHandler.execute` - Execute an action in a tournament game

## Events

- `onSngTournamentList` - List of available tournaments
- `onSngTournamentJoin` - Player joined a tournament
- `onSngTournamentStatus` - Tournament status update
- `onSngBlindUpdate` - Blind level update
- `onSngTournamentResult` - Tournament results
- `onSngPlayerEliminated` - Player eliminated from tournament

## Integration with Existing Game

The SNG tournament feature integrates with the existing poker game by:

1. Using the same table and game logic
2. Adding tournament-specific features like blind increases and player elimination
3. Tracking tournament progress and results
4. Managing tournament rewards

## Configuration

Tournament settings can be configured in:

- `game-server/app/consts/sngConsts.js` - Constants for SNG tournaments
- `game-server/config/data/sngTournaments.json` - Tournament configuration

## Testing

Use the test script `game-server/test/sngTournamentTest.js` to test the SNG tournament functionality.

## Tournament Logging System

The SNG tournament feature includes a comprehensive logging system that tracks all important actions and events during a tournament's lifecycle. This is implemented through the `sng_tournament_logs` table, which stores detailed information about:

- Player registrations
- Tournament status changes
- Blind level increases
- Player eliminations
- Prize distributions
- Tournament completions

Each log entry includes:

- Tournament ID
- Player ID (if applicable)
- Action type
- Detailed data in JSON format
- Amount (if applicable)
- Timestamp

This logging system provides:

1. Complete audit trail of tournament actions
2. Data for analytics and reporting
3. Troubleshooting capabilities for resolving disputes
4. Historical performance metrics

The `logTournamentAction` function in `sngTournamentService.js` handles all logging operations, ensuring consistent and reliable record-keeping across the tournament system.

## Future Improvements

- Multi-table tournaments
- Tournament leaderboards
- Tournament statistics
- Tournament chat
- Tournament spectating
- Enhanced analytics based on tournament logs

## Tóm tắt các cải tiến

Tôi đã thực hiện các cải tiến sau đây để tăng cường chức năng tính toán thắng thua, cập nhật thứ hạng và chia thưởng trong giải đấu SNG:

### 1. Cải thiện xử lý loại bỏ người chơi:

- Kiểm tra người chơi đã bị loại trước đó chưa
- Xác định người chiến thắng khi chỉ còn một người chơi
- Ghi log chi tiết hơn về quá trình loại bỏ người chơi

### 2. Thêm phương thức xử lý kết thúc giải đấu:

- Thêm phương thức handleTournamentEnd để xử lý khi có người chiến thắng
- Cập nhật trạng thái người chiến thắng và kết thúc giải đấu

### 3. Cải thiện phân phối giải thưởng:

- Điều chỉnh tỷ lệ phân phối dựa trên số lượng người chơi
- Xử lý đúng các trường hợp đặc biệt (1 hoặc 2 người chơi)
- Ghi log chi tiết về quá trình phân phối giải thưởng

### 4. Cải thiện thông báo kết quả giải đấu:

- Lấy thông tin giải thưởng từ cơ sở dữ liệu
- Tính toán giải thưởng dự phòng nếu chưa có trong cơ sở dữ liệu
- Ghi log chi tiết về thông báo kết quả

### 5. Cải thiện xử lý thứ hạng người chơi:

- Sắp xếp người chơi theo trạng thái (ACTIVE trước, ELIMINATED sau)
- Tính toán thứ hạng dựa trên số người chơi còn hoạt động
- Ghi log chi tiết về thứ hạng người chơi

### 6. Cải thiện kết thúc giải đấu:

- Thu thập thống kê chi tiết về giải đấu
- Ghi log chi tiết về quá trình kết thúc giải đấu
- Xử lý dọn dẹp tài nguyên giải đấu

### 7. Cải thiện thông báo loại bỏ người chơi:

- Thêm thông tin về số người chơi còn lại
- Thêm thông tin về mức blind hiện tại
- Ghi log chi tiết về thông báo loại bỏ người chơi

### 8. Cải thiện xử lý tăng mức blind:

- Lưu trữ mức blind hiện tại trong bộ nhớ
- Kiểm tra số người chơi còn hoạt động trước khi tăng mức blind
- Ghi log chi tiết về quá trình tăng mức blind

### 9. Cải thiện lấy mức blind hiện tại:

- Lấy mức blind từ bộ nhớ nếu có
- Mặc định về mức 1 nếu không tìm thấy

### 10. Cải thiện thông báo tăng mức blind:

- Thêm thông tin về số người chơi còn hoạt động
- Thêm thông tin về thời gian tăng mức blind tiếp theo
- Ghi log chi tiết về thông báo tăng mức blind

### 11. Thêm hệ thống ghi log chi tiết cho giải đấu:

- Tạo bảng `sng_tournament_logs` để lưu trữ chi tiết các hành động trong giải đấu
- Cập nhật hàm `logTournamentAction` để ghi log vào bảng mới
- Lưu trữ đầy đủ thông tin về người chơi, loại hành động, dữ liệu chi tiết và thời gian
- Hỗ trợ truy vấn và phân tích dữ liệu giải đấu
- Tạo cơ sở dữ liệu cho việc giải quyết tranh chấp và báo cáo thống kê

Các cải tiến này đảm bảo rằng chức năng tính toán thắng thua, cập nhật thứ hạng và chia thưởng trong giải đấu SNG hoạt động chính xác và đáng tin cậy, đồng thời không ảnh hưởng đến luồng chơi tự do hiện có. Hệ thống ghi log chi tiết cung cấp khả năng theo dõi và phân tích toàn diện cho các hoạt động của giải đấu.
